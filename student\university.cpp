#include "university.h"
using namespace std;
ostream& operator<<(ostream& o, const Student& s) {
	o << s.getId() << ". " << s.getName();
	return o;
}

string Student::getName() const{
	return name;
}

string Student::getId() const {
	cout << "TEST2" << endl;
	return id;
}

void Student::setName(string name) {
	this->name = name;
}

Student::Student() {
	name = "";
}
Student::Student(string name, string id) {
	(*this).name = name;
	this->id = id;
}

ostream& operator<<(ostream& o, const Roster& r) {
	for(int i = 0; i < r.size; i++) {
		o << r.students[i] << endl;
	}
	return o;
}

Roster::Roster() {
	capacity = 2;
	size = 0;
	
	students = new Student[capacity];
}

Roster::Roster(const Roster& r){
	//copy static memory
	capacity = r.capacity;
	size = r.size;
	
	//copy dynamic memory
	students = new Student[capacity];
	for(int i = 0; i < size; i++) {
		students[i] = r.students[i];
	}
}

Roster::~Roster() {
	delete [] students;
}

		//a = a = a; //self-reassignment 
Roster& Roster::operator=(const Roster& r) {
	if(this != &r) {		
		//copy static memory
		this-> capacity = r.capacity;
		this -> size = r.size;

		//copy dynamic memory
		delete [] students;

		this -> students = new Student[capacity];
		for(int i = 0; i < size; i++) {
			students[i] = r.students[i];
		}
	}
	
	return *this;
}

void Roster::Add(Student s) {

	if(size == capacity) {
		//sad path
	}
	//happy path
	students[size++] = s;
}	

int Roster::getSize() const {
	return size;
}

Student* Roster::getStudents() const {
	return students;
}

void Roster::Update(string id) {
	//find the student with the id we care about
	int index = -9;
	for(int i = 0; i <= size; i++) {
		cout << "TEST" << endl;
		cout << "Looking for: id " << students[i].getId() << endl;
		if(students[i].getId() == id) {
			index = i;
			break;
		}
	}
	
	//accept new values to update that student
	cout << students[index] << endl;
	string newName = "";
	cout << "Name: " << students[index].getName() << " -> " ;
	cin >> newName;
	students[index].setName(newName);
	cout << "Student record updated!" << endl;
	cout << students[index] << endl;
}
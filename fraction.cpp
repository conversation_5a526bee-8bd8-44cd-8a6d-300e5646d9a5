#include "fraction.h"
int Fraction::getNum() const
{
	return num;
}

void Fraction::setNum(int n)
{
	num = n;
}

int Fraction::getDen() const
{
	return den;
}

void Fraction::setDen(int d)
{
	den = d;
}

void Fraction::print() const
{
	cout << getNum() << "|" << getDen() << endl;
}

Fraction::Fraction()
{
	den = 1;
	num = 0;
}

Fraction::Fraction(int n)
{
	den = 1;
	num = n;
}

Fraction::Fraction(int n, int d)
{
	den = d;
	num = n;
}

double Fraction::getDouble()
{
	return (num * 1.0) / den;
}

int Fraction::gcd(int a, int b) const
{
	while (a != b)
	{
		if (a > b)
		{
			a -= b;
		}
		else
		{
			b -= a;
		}
	}
	return a;
}

Fraction Fraction::add(Fraction f) const
{
	int newNum = num * f.den + f.num * den;
	int newDen = f.den * den;

	int GCD = gcd(newNum, newDen);

	return Fraction(newNum / GCD, newDen / GCD);
}

Fraction operator+(Fraction f1, Fraction f2)
{
	int newNum = f1.num * f2.den + f2.num * f1.den;
	int newDen = f2.den * f1.den;

	int GCD = f1.gcd(newNum, newDen);

	return Fraction(newNum / GCD, newDen / GCD);
}

ostream &operator<<(ostream &o, const Fraction &f)
{
	o << f.getNum() << "|" << f.getDen();
	return o;
}
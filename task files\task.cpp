#include "task.h"
#include <iostream>
#include <cstring>

using namespace std;

// task container
TaskContainer::TaskContainer()
{
    numTasks = 0;
    maxTasks = 2;
    tasks = new Task[maxTasks];
}
// deep copy constructor
TaskContainer::TaskContainer(const TaskContainer &container)
{
    numTasks = container.numTasks;
    maxTasks = container.maxTasks;
    tasks = new Task[maxTasks];
    for (int i = 0; i < numTasks; ++i)
    {
        tasks[i] = container.tasks[i];
    }
}
// deep copy assignment operator overload
TaskContainer &TaskContainer::operator=(const TaskContainer &container)
{
    if (this != &container)
    {
        delete[] tasks;
        numTasks = container.numTasks;
        maxTasks = container.maxTasks;
        tasks = new Task[maxTasks];
        for (int i = 0; i < numTasks; ++i)
        {
            tasks[i] = container.tasks[i];
        }
    }
    return *this;
}
// destructor
TaskContainer::~TaskContainer()
{
    delete[] tasks;
}

void TaskContainer::resize()
{
    maxTasks += 2;
    Task *newTasks = new Task[maxTasks];
    for (int i = 0; i < numTasks; ++i)
        newTasks[i] = tasks[i];

    delete[] tasks;
    tasks = newTasks;
}
// crud implementation
void TaskContainer::createTask(const Task &t)
{
    if (numTasks == maxTasks)
        resize();
    tasks[numTasks++] = t;
}

bool TaskContainer::updateTask(const string &title, int choice, const string &newVal)
{
    int index = findTask(title);
    if (index == -1)
        return false;
    if (choice == 1)

        tasks[index].setIsCompleted(true);

    else if (choice == 2)

        tasks[index].setDueDate(newVal);

    else

        return false;

    return true;
}

bool TaskContainer::deleteTask(const string &title)
{
    int index = findTask(title);
    if (index == -1)
        return false;
    for (int i = index; i < numTasks - 1; ++i)
    {
        tasks[i] = tasks[i + 1];
    }
    numTasks--;
    return true;
}

int TaskContainer::getNumTasks() const
{
    return numTasks;
}
const Task &TaskContainer::getTask(int index) const
{
    return tasks[index];
}
int TaskContainer::findTask(const string &title) const
{
    for (int i = 0; i < numTasks; ++i)
    {
        if (tasks[i].getTitle() == title)
            return i;
    }
    return -1;
}
// insertion operator overload
ostream &operator<<(ostream &o, const TaskContainer &tc)
{
    for (int i = 0; i < tc.numTasks; ++i)
    {
        o << "Task " << (i + 1) << ":\n";
        o << tc.tasks[i] << endl;
    }
    return o;
}

Task::Task()
{
    title = "";
    description = "";
    dueDate = "";
    isCompleted = false;
}

Task::Task(string title, string description, string dueDate, bool isCompleted)
{
    this->title = title;
    this->description = description;
    this->dueDate = dueDate;
    this->isCompleted = isCompleted;
}

string Task::getTitle() const
{
    return title;
}
string Task::getDescription() const
{
    return description;
}
string Task::getDueDate() const
{
    return dueDate;
}
bool Task::getIsCompleted() const
{
    return isCompleted;
}

void Task::setTitle(const string &title)
{
    this->title = title;
}
void Task::setDescription(const string &description)
{
    this->description = description;
}
void Task::setDueDate(const string &dueDate)
{
    this->dueDate = dueDate;
}
void Task::setIsCompleted(bool completed)
{
    this->isCompleted = completed;
}

// Insertion operator
ostream &operator<<(ostream &o, const Task &t)
{
    o << "Title: " << t.title << endl
      << "Description: " << t.description << endl
      << "Due Date: " << t.dueDate << endl
      << "Completed: " << (t.isCompleted ? "Yes" : "No");
    return o;
}

// Extraction operator bonus
istream &operator>>(istream &i, Task &t)
{
    cout << "Enter Title: ";
    getline(i, t.title);
    cout << "Enter Description: ";
    getline(i, t.description);
    cout << "Enter Due Date: ";
    getline(i, t.dueDate);
    cout << "Is Completed (0 = No, 1 = Yes): ";
    i >> t.isCompleted;
    return i;
}

#include <iostream>
#include <string>
using namespace std;
class Task
{
    friend ostream &operator<<(ostream &o, const Task &t);
    friend istream &operator>>(istream &i, Task &t);

private:
    string title;
    string description;
    string dueDate;
    bool isCompleted;

public:
    Task();
    Task(string title, string description, string dueDate, bool isCompleted);
    string getTitle() const;
    string getDescription() const;
    string getDueDate() const;
    bool getIsCompleted() const;
    void setTitle(const string &title);
    void setDescription(const string &description);
    void setDueDate(const string &dueDate);
    void setIsCompleted(bool completed);
};

class TaskContainer
{
    friend ostream &operator<<(ostream &o, const TaskContainer &tc);

private:
    Task *tasks;
    int numTasks;
    int maxTasks;
    void resize();

public:
    TaskContainer();
    TaskContainer(const TaskContainer &container);
    TaskContainer &operator=(const TaskContainer &container);
    ~TaskContainer();

    void createTask(const Task &t);
    bool updateTask(const string &title, int choice, const string &newVal = "");
    bool deleteTask(const string &title);
    int getNumTasks() const;
    const Task &getTask(int index) const;
    int findTask(const string &title) const;
};

#ifndef _SIMPLELIST_HPP
#define _SIMPLELIST_HPP

template <class T>
SimpleList<T>::SimpleList()
{
    current = 0;
    array = new T *[MAX];
    count = 0;
}

template <class T>
SimpleList<T>::~SimpleList()
{
    for (int i = 0; i < count; i++)
        delete array[i];
    delete[] array;
}

template <class T>
void SimpleList<T>::add(T *item)
{
    if (count == MAX)
        throw FullListException();
    array[count++] = item;
}
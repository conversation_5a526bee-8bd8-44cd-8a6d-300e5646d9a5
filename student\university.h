#include<string>
#include<iostream>
using namespace std;
class Student{
	friend ostream& operator<<(ostream& o, const Student& s);
	private:
		string name;
		string id;
	public:
		string getName() const;
		void setName(string name);
		Student();
		Student(string name, string id);
		string getId() const;
		
};

class Roster {
	friend ostream& operator<<(ostream& o, const Roster& r);
	private:
		Student* students;
		int capacity, size;
		
	public:
		Roster();
		~Roster();
		
		//Roster a = b;
		Roster(const Roster& r);
		
		//Roster b;
		// Roster a = b;
		//a = b;
		//a.operator=(b);
		Roster& operator=(const Roster& r);
		
		void Add(Student s);
		void Update(string id);
		
		int getSize() const;
		Student* getStudents() const;
};
#include "complex.h"
#include <iostream>

// default constructor
Complex::Complex()
{
    real = 0;
    imag = 0;
}

// conversion constructor
Complex::Complex(double r)
{
    real = r;
    imag = 0;
}

// parameterized constructor
Complex::Complex(double r, double i)
{
    real = r;
    imag = i;
}

// member function for addition
Complex Complex::operator+(const Complex &other) const
{
    return Complex(real + other.real, imag + other.imag);
}

// member function for subtraction
Complex Complex::operator-(const Complex &other) const
{
    return Complex(real - other.real, imag - other.imag);
}

// friend function for negation
Complex operator-(const Complex &c)
{
    return Complex(-c.real, -c.imag);
}

// friend function for multiplication
Complex operator*(const Complex &c1, const Complex &c2)
{
    return Complex(c1.real * c2.real - c1.imag * c2.imag, c1.real * c2.imag + c1.imag * c2.real);
}

// member function for complement
Complex Complex::complement() const
{
    return Complex(real, -imag);
}

// overload for insertion operator
ostream &operator<<(ostream &os, const Complex &c)
{
    os << c.real << (c.imag >= 0 ? "+" : "") << c.imag << "i";
    return os;
}

// equality operator
bool Complex::operator==(const Complex &other) const
{
    return real == other.real && imag == other.imag;
}
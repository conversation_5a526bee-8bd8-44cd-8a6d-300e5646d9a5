#ifndef PLAYERCONTAINER_H
#define PLAYERCONTAINER_H

#include "duck.h"

class PlayerContainer
{
private:
    Player **players;
    int size;
    int capacity;

public:
    // Constructor
    PlayerContainer(int initialCapacity = 10);

    // Rule of Three
    // 1. Destructor
    ~PlayerContainer();

    // 2. Copy Constructor
    PlayerContainer(const PlayerContainer &other);

    // 3. Assignment Operator
    PlayerContainer &operator=(const PlayerContainer &other);

    // Member functions
    void addPlayer(Player *player);
    void playGame();
    void setGooseAtIndex(int index);
    int getSize() const;
    Player *getPlayer(int index) const;
    void clear();

private:
    void resize();
    void copyFrom(const PlayerContainer &other);
};

#endif
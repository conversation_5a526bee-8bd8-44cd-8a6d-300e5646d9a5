#include <iostream>
using namespace std;

void fizzBuzz(int div1, int div2, int bound);

int main()
{
    int div1, div2, bound;

    cout << "Welcome to FizzBuzz 2." << endl;
    cout << "Enter your maximum bounds: ";
    cin >> bound;
    cout << "Enter your first divisor: ";
    cin >> div1;
    cout << "Enter your second divisor: ";
    cin >> div2;

    fizzBuzz(div1, div2, bound);

    return 0;
}

void fizzBuzz(int div1, int div2, int bound)
{
    for (int i = 1; i <= bound; i++)
    {
        if (i % div1 == 0 && i % div2 == 0)
        {
            cout << "FizzBuzz ";
            if (i % 10 == 0)
            {
                cout << endl;
            }
        }
        else if (i % div1 == 0)
        {
            cout << "Fizz ";
            if (i % 10 == 0)
            {
                cout << endl;
            }
        }
        else if (i % div2 == 0)
        {
            cout << "Buzz ";
            if (i % 10 == 0)
            {
                cout << endl;
            }
        }
        else
        {
            cout << i << " ";
            if (i % 10 == 0)
            {
                cout << endl;
            }
        }
    }
}
#include "university.h"

int main()
{

	int numStudents = 0, maxStudents = 2;
	Student *students = new Student[maxStudents];
	Roster r;
	char selection = '\0';
	do
	{
		cout << "Student Menu: " << endl;
		cout << "C. Create Student Record" << endl;
		cout << "R. List all Student Records" << endl;
		cout << "U. Update Student Record" << endl;
		cout << "D. Delete Student Record" << endl;
		cout << "Q. Quit" << endl;
		cin >> selection;

		switch (selection)
		{
		case 'c':
		case 'C':
		{
			string name, id;
			cout << "Name: ";
			cin >> name;
			cout << "Id: ";
			cin >> id;
			if (numStudents == maxStudents)
			{
				// sad path
			}
			// happy path
			// students[numStudents++] = Student(name, id);

			r.Add(Student(name, id));
			break;
		}
		case 'r':
		case 'R':
			cout << r << endl;
			break;
		case 'u':
		case 'U':
		{
			cout << r << endl;
			cout << "Id to Update: " << endl;
			string id;
			cin >> id;
			int index = -1;
			cout << "Number of students = " << r.getSize() << endl;

			/*for(int i = 0; i <= r.getSize(); i++) {
				cout << "TEST" << endl;
				cout << "Looking for: id " << r.getStudents()[i].getId() << endl;
				if(r.getStudents()[i].getId() == id) {
					index = i;
					break;
				}
			}
			cout << "TEST3" << endl;
			cout << r.getStudents()[index] << endl;
			string newName = "";
			cout << "Name: " << r.getStudents()[index].getName() << " -> " ;
			cin >> newName;
			r.getStudents()[index].setName(newName);
			cout << "Student record updated!" << endl;
			cout << r.getStudents()[index] << endl;*/
			r.Update(id);
			break;
		}
		case 'd':
		case 'D':
		{
			cout << "Id to Delete: " << endl;
			string id;
			cin >> id;
			int index = -1;
			for (int i = 0; i < numStudents; i++)
			{
				if (students[i].getId() == id)
				{
					index = i;
					break;
				}
			}

			for (int i = index; i < numStudents - 1; i++)
			{
				students[i] = students[i + 1];
			}
			numStudents--;

			break;
		}
		case 'q':
		case 'Q':
			break;
		default:
			break;
		}
	} while (selection != 'q' && selection != 'Q');

	delete[] students;
}

#include <iostream>
using namespace std;
class Fraction
{
	friend Fraction operator+(Fraction f1, Fraction f2);
	friend ostream &operator<<(ostream &o, const Fraction &f);

private:
	int num, den;
	int gcd(int a, int b) const;

public:
	int getNum() const;
	void setNum(int n);

	int getDen() const;
	void setDen(int d);

	void print() const;

	Fraction();				// default constructor
	Fraction(int n);		// conversion constructor int => Fraction
	Fraction(int n, int d); // parameterized constructor

	double getDouble();

	Fraction add(Fraction f) const;
};
#include "task.h"
#include <iostream>
using namespace std;

int main()
{
    TaskContainer tc;
    char selection = '\0';
    do
    {
        cout << endl
             << "Task Menu:" << endl;
        cout << "C. Create Task" << endl;
        cout << "R. List all Tasks" << endl;
        cout << "U. Update Task" << endl;
        cout << "D. Delete Task" << endl;
        cout << "Q. Quit" << endl;
        cout << "Selection: ";
        cin >> selection;

        switch (selection)
        {
        case 'c':
        case 'C':
        {
            Task task;
            cin.ignore();
            cin >> task;
            tc.createTask(task);
            cout << "Task added!" << endl;
            break;
        }
        case 'r':
        case 'R':
            cout << endl;
            cout << tc;
            break;
        case 'u':
        case 'U':
        {
            cout << tc;
            cout << "Enter Title of Task to Update: ";
            string title;
            cin.ignore();
            getline(cin, title);
            if (tc.findTask(title) == -1)
            {
                cout << "Task not found." << endl;
                break;
            }
            cout << "1. Mark as completed\n2. Change Due Date\nChoice: ";
            int choice;
            cin >> choice;
            string newDue;
            if (choice == 2)
            {
                cout << "Enter new Due Date: ";
                getline(cin, newDue);
            }
            if (tc.updateTask(title, choice, newDue))
            {
                if (choice == 1)
                    cout << "Task marked as completed." << endl;
                else if (choice == 2)
                    cout << "Due Date updated." << endl;
            }
            else
            {
                cout << "Update failed." << endl;
            }
            break;
        }
        case 'd':
        case 'D':
        {
            cout << tc;
            cout << "Enter Title of Task to Delete: ";
            string title;
            cin.ignore();
            getline(cin, title);
            if (tc.deleteTask(title))
            {
                cout << "Task deleted." << endl;
            }
            else
            {
                cout << "Task not found." << endl;
            }
            break;
        }
        case 'q':
        case 'Q':
            break;
        default:
            cout << "Invalid selection." << endl;
            break;
        }
    } while (selection != 'q' && selection != 'Q');
}
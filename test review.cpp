#include <iostream>
using namespace std;
class Weapon
{
    friend ostream &operator<<(ostream &o, const Weapon &w);

private:
    string name;
    int damage;

public:
    Weapon();
    Weapon(const string &n, int d) : name(n), damage(d) {}
};
class Inventory
{
    friend ostream &operator<<(ostream &o, const Inventory &i);

private:
    Weapon *content;
    int capacity, size;

public:
    ~Inventory();                  // Question number 38
    Inventory(const Inventory &i); // Question number 39
    Inventory();
    Inventory &operator=(const Inventory &i);
    Inventory operator+(const Inventory &inv);
    void Add(const Weapon &w);
    Weapon Remove(int i);
    Weapon &Get(int i);
};
Inventory::Inventory()
{
    capacity = 3;
    size = 0;
    content = new Weapon[capacity];
}
void Inventory::Add(const Weapon &w)
{
    if (capacity == size)
    {
        // sad path
        capacity += 7;
        Weapon *temp = new Weapon[capacity];
        for (int i = 0; i < size; i++)
        {
            temp[i] = content[i];
        }
        delete[] content;
        content = temp;
    }
    // happy path
    content[size] = w;
    size++;
}
// Section 3 : Writing Code(20 points – 5 points a piece) 40. Provide the definition of the deep copy assignment operator overload for Inventory.
Inventory &Inventory::operator=(const Inventory &i)
{
    // Optionally check for self-assignment
    // if (this == &i) return *this;

    // Copy the static members
    capacity = i.capacity; // Copy capacity
    size = i.size;         // Copy size

    // Copy the dynamic array
    delete[] content;               // Free old memory
    content = new Weapon[capacity]; // Allocate new array
    for (int j = 0; j < size; j++)
    {
        content[j] = i.content[j]; // Copy each Weapon
    }
    return *this; // Return reference to this object
}
// 41. Provide the definition of the Remove function for Inventory that matches the given declaration on the previouspage.
Weapon Inventory::Remove(int i)
{
    Weapon returnW = content[i]; // Store weapon to return
    // Shift all elements after i left by one
    for (int j = i; j < size - 1; j++)
    {
        content[j] = content[j + 1]; // Overwrite current with next
    }
    size--;         // Decrement size
    return returnW; // Return removed weapon
}
// 42. Provide a definition for the insertion operator overload for Inventory that prints all weapons in the inventoryproceeded by the index of that weapon. For example, the 2nd weapon should be printed: “1. <weapon>” where<weapon> represents the result of the insertion operator for the Weapon class.
ostream &operator<<(ostream &o, const Inventory &inv)
{
    // Print each weapon with its index
    for (int i = 0; i < inv.size; i++)
    {
        o << i << "." << inv.content[i] << endl; // Uses Weapon's << operator
    }
    return o; // Return stream for chaining
}
// 43. Provide the definition of a + operator overload that returns the result of appending the contents of one Inventory to the contents of another Inventory.
Inventory Inventory::operator+(const Inventory &inv)
{
    Inventory retInv;                          // Create new Inventory
    retInv.size = 0;                           // Start empty
    retInv.capacity = capacity + inv.capacity; // Combined capacity
    // Delete default array allocated by constructor
    delete[] retInv.content;
    retInv.content = new Weapon[retInv.capacity]; // Allocate new array

    // Copy all weapons from this inventory
    for (int i = 0; i < size; i++)
    {
        retInv.content[i] = content[i];
        retInv.size++;
    }
    // Copy all weapons from the other inventory
    for (int i = 0; i < inv.size; i++)
    {
        retInv.content[retInv.size + i] = inv.content[i];
        retInv.size++;
    }
    return retInv; // Return combined inventory
}
// Bonus (5 points): Given a dynamically allocated list of Shapes called myShapes of size 10, provide the algorithm forresizing the array to size 25.
// Shape *myShapes = new Shape[10];
// Shape *temp = new Shape[15];
// for (int i = 0; i < 10; i++)
//{
//  temp[i] = myShapes[i];
//}
// delete[] myShapes;
#include "complex.h"
#include <iostream>
using namespace std;

int main()
{
    // default constructor
    Complex c1;
    cout << "Default constructor: " << c1 << endl;

    // conversion constructor
    Complex c2(5.0);
    cout << "Conversion constructor: " << c2 << endl;

    // parameterized constructor
    Complex c3(3.0, 4.0);
    cout << "Parameterized constructor: " << c3 << endl;

    // addition
    Complex c4 = c2 + c3;
    cout << "Addition: " << c4 << endl;

    // subtraction
    Complex c5 = c3 - c2;
    cout << "Subtraction: " << c5 << endl;

    // negation
    Complex c6 = -c3;
    cout << "Negation: " << c6 << endl;

    // multiplication
    Complex c7 = c3 * c2;
    cout << "Multiplication: " << c7 << endl;

    // complement
    Complex c8 = c3.complement();
    cout << "Complement: " << c8 << endl;

    // equality
    bool isEqual = (c3 == Complex(3.0, 4.0));
    cout << "Equality: " << (isEqual ? "True" : "False") << endl;

    return 0;
}
#include <iostream>

using namespace std;

const int MAX = 10;

template <class T>
class SimpleList
{
private:
    T **array;
    int current;

public:
    SimpleList();
    ~SimpleList();
    bool Insert(T item);
    T *Remove(unsigned int index);
    T *getElement(unsigned int n);
    void Print();
    void PrintHalf(bool secondHalf);
    int getSize();

    SimpleList(const SimpleList<T> &s);    // copy constuctor
    SimpleList<T>(const SimpleList<u> &s); // copy constuctor
};

#include "simplelist.hpp"

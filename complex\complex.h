#include <iostream>
using namespace std;

class Complex
{

    friend ostream &operator<<(ostream &os, const Complex &c);
    friend Complex operator-(const Complex &c);
    friend Complex operator*(const Complex &c1, const Complex &c2);

private:
    double real;
    double imag;

public:
    Complex();
    Complex(double r);
    Complex(double r, double i);
    Complex operator+(const Complex &other) const;
    Complex operator-(const Complex &other) const;
    Complex complement() const;
    bool operator==(const Complex &other) const;
};